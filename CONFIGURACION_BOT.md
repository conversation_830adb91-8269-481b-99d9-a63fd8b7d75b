# CubaMax Tracker Bot - Configuración

## 📋 Descripción
Bot de Telegram que rastrea automáticamente envíos de CubaMax usando web scraping con la biblioteca Cheerio de Google Apps Script.

## 🚀 Configuración Inicial

### 1. <PERSON><PERSON><PERSON> Google Spreadsheet
Crea una nueva hoja de cálculo de Google Sheets con las siguientes hojas:

#### Hoja: `BOT-TOKEN`
- **A1**: "BOT TOKEN"
- **A2**: Tu token del bot de Telegram (obtenido de @BotFather)

#### Hoja: `USERS`
- **A1**: "USER NAME"
- **B1**: "USER ID" 
- **C1**: "USER NUMBER"
- **D1**: "USER MAIL"
- **E1**: "TRACKING NUMBER"
- **F1**: "TIME"
- **G1**: "NOTIFICATION FREQUENCY"
- **H1**: "LAST NOTIFICATION"

#### Hoja: `USER DB`
- **A1**: "USER SHEET URL"
- **A2**: URL de otra hoja de cálculo donde se guardarán los datos individuales de cada usuario

#### Hoja: `TIME`
- **A1**: "USER ID"
- **B1**: "ENTRY TIME"

### 2. Configurar Google Apps Script

1. Ve a [script.google.com](https://script.google.com)
2. Crea un nuevo proyecto
3. Copia todo el código de `CUBAMAXTRACKER.gs`
4. Reemplaza `TU_SPREADSHEET_ID_AQUI` con el ID de tu Google Spreadsheet
5. Habilita la biblioteca Cheerio:
   - Ve a Bibliotecas → Agregar biblioteca
   - ID de script: `1ReeQ6WO8kKNxoaA_O0XEQ589cIrRvEBA9qcWpNqdOP17i47u6N9M5Xh0`
   - Selecciona la versión más reciente
   - Guarda

### 3. Configurar Webhook

1. En Google Apps Script, ve a Implementar → Nueva implementación
2. Tipo: Aplicación web
3. Ejecutar como: Yo
4. Acceso: Cualquier persona
5. Copia la URL de la aplicación web
6. Reemplaza `TU_WEB_APP_URL_AQUI` en la función `setWebhook()`
7. Ejecuta la función `setWebhook()` una vez

### 4. Configurar Triggers Automáticos

Ejecuta la función `setupTriggers()` una vez para configurar las notificaciones automáticas diarias.

## 🤖 Comandos del Bot

- `/start` - Iniciar bot y registrarse
- `/track` - Rastrear envío actual
- `/settings` - Configurar frecuencia de notificaciones
- `/status` - Ver estado actual del envío

## 📦 Funcionalidades

### ✅ Registro de Usuario
- Captura nombre de usuario de Telegram
- Solicita número de teléfono
- Requiere email Gmail
- Pide código de tracking de CubaMax
- Guarda fecha y hora de registro

### 🔍 Web Scraping
- Extrae datos de https://m.cubamax.com/trk/
- Obtiene información del cliente (nombre, HBL)
- Rastrea todos los eventos del envío
- Identifica estados completados vs pendientes

### 📊 Gestión de Datos
- Hoja principal con datos de usuarios
- Hojas individuales por usuario en spreadsheet separado
- Historial completo de tracking
- Timestamps de todas las actualizaciones

### 🔔 Notificaciones Automáticas
- Frecuencias configurables: cada hora, diario, semanal
- Notificaciones automáticas sobre cambios de estado
- Resumen del último evento completado
- Información del próximo evento pendiente

### ⚙️ Configuración Personalizada
- Menú de configuración con botones inline
- Cambio de frecuencia de notificaciones
- Estado actual del envío en tiempo real

## 📋 Estructura de Datos

### Eventos de Tracking Detectados:
1. **Created** - Orden Creada
2. **Check-In** - Recibido en Almacén
3. **Shipped** - Montado en contenedor
4. **Arrived** - Revisión aduanal en destino
5. **Ready for Distribution** - Carga liberada por aduana
6. **Delivered** - Entregado

### Formato de Datos Guardados:
```json
{
  "trackingCode": "EDF6V-BPF",
  "timestamp": "2025-07-13T10:30:00Z",
  "clientName": "MARTHA TORRES POZO",
  "hbl": "CMT25569575",
  "events": [
    {
      "status": "Created",
      "date": "Jul 07, 2025 02:08 PM",
      "description": "Orden Creada.",
      "completed": true
    }
  ]
}
```

## 🛠️ Mantenimiento

### Logs y Errores
- Todos los errores se registran en console.log
- Verificar logs en Google Apps Script regularmente

### Actualizaciones
- El bot verifica cambios automáticamente
- Los datos se guardan con timestamps
- Historial completo mantenido en hojas individuales

### Backup
- Datos principales en Google Sheets
- Datos detallados en spreadsheet secundario
- Exportación automática disponible

## 🔧 Personalización

### Modificar Frecuencias
Edita las opciones en `showSettingsMenu()`:
```javascript
const keyboard = {
  inline_keyboard: [
    [
      { text: '⏰ Cada hora', callback_data: 'freq_hourly' },
      { text: '📅 Diario', callback_data: 'freq_daily' }
    ],
    [
      { text: '📆 Semanal', callback_data: 'freq_weekly' }
    ]
  ]
};
```

### Cambiar Hora de Notificaciones
Modifica en `setupTriggers()`:
```javascript
ScriptApp.newTrigger('sendDailyNotifications')
  .timeBased()
  .everyDays(1)
  .atHour(9) // Cambiar hora aquí
  .create();
```

## 📞 Soporte

Para problemas o mejoras:
1. Verificar logs en Google Apps Script
2. Comprobar configuración de hojas
3. Validar tokens y URLs
4. Revisar permisos de Google Sheets

## 🔒 Seguridad

- Token del bot almacenado en Google Sheets
- Validación de emails Gmail únicamente
- Códigos de tracking validados con regex
- Manejo de errores robusto
