/**
 * CubaMax Tracker Bot - Versión Funcional
 * Bot de Telegram para rastrear envíos de CubaMax
 */

var TELEGRAM_TOKEN = ''; // Se obtiene de la hoja BOT-TOKEN
var SPREADSHEET_ID = '1ELC6g_6GPXzI36MUoy0tiiYHQy0vsiU9In3LKubsHvc';

var userStates = {};

function doPost(e) {
  try {
    var data = JSON.parse(e.postData.contents);
    var chatId = data.message.chat.id;
    var text = data.message.text;
    var userName = data.message.from.first_name || data.message.from.username;
    var userId = data.message.from.id;

    var userState = userStates[chatId] || 'normal';

    if (text === '/start') {
      handleStartCommand(chatId, userId, userName);
    } else if (text === '/status') {
      showUserStatus(chatId, userId);
    } else if (text === '/help') {
      sendHelpMessage(chatId);
    } else if (text === '/stop') {
      userStates[chatId] = 'normal';
      sendText(chatId, '🛑 Regresaste al modo normal.');
    } else {
      if (userState === 'normal') {
        sendText(chatId, 'Usa /start para comenzar o /help para ver comandos disponibles.');
      } else {
        handleUserInput(chatId, userId, userName, text, userState);
      }
    }

    return ContentService.createTextOutput('OK');
  } catch (error) {
    console.error('Error:', error);
    return ContentService.createTextOutput('Error');
  }
}

function sendText(chatId, text) {
  var token = getBotToken();
  var url = `https://api.telegram.org/bot${token}/sendMessage`;
  var payload = {
    'method': 'post',
    'payload': {
      'chat_id': String(chatId),
      'text': text,
      'parse_mode': 'Markdown'
    }
  };
  UrlFetchApp.fetch(url, payload);
}

function getBotToken() {
  var sheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName('BOT-TOKEN');
  if (!sheet) {
    console.error('Hoja BOT-TOKEN no existe');
    return '';
  }
  return sheet.getRange('A2').getValue();
}

function sendHelpMessage(chatId) {
  var helpText =
    `*📦 CubaMax Tracker Bot*\n\n` +
    `*Comandos Disponibles:*\n\n` +
    `/start - Iniciar bot o registrarse 🚀\n` +
    `/status - Ver estado del envío 📊\n` +
    `/help - Mostrar esta ayuda 📖\n` +
    `/stop - Parar proceso actual 🛑\n\n` +
    `*¿Cómo funciona?*\n` +
    `1. Usa /start para registrarte\n` +
    `2. Proporciona tus datos\n` +
    `3. Recibe actualizaciones automáticas\n\n` +
    `Bot creado para rastrear envíos de CubaMax 📦`;

  sendText(chatId, helpText);
}

function handleStartCommand(chatId, userId, userName) {
  var user = getUserData(userId);

  if (user) {
    // Usuario existente
    sendText(chatId, `¡Bienvenido de nuevo, ${userName}! 👋\n\n📦 *Tu tracking:* ${user.trackingNumber}\n📊 *Estado actual:* ${user.status}\n\nUsa /status para ver detalles actualizados.`);
  } else {
    // Usuario nuevo
    sendText(chatId, `¡Hola ${userName}! 👋\n\nBienvenido al *CubaMax Tracker Bot* 📦\n\nPara comenzar, envía tu número de teléfono:`);
    userStates[chatId] = 'waiting_phone';
  }
}

function handleUserInput(chatId, userId, userName, text, userState) {
  if (userState === 'waiting_phone') {
    handlePhoneInput(chatId, userId, userName, text);
  } else if (userState === 'waiting_email') {
    handleEmailInput(chatId, userId, text);
  } else if (userState === 'waiting_tracking') {
    handleTrackingInput(chatId, userId, text);
  } else if (userState === 'waiting_frequency') {
    handleFrequencyInput(chatId, userId, text);
  }
}

function handlePhoneInput(chatId, userId, userName, phone) {
  if (phone.length < 8) {
    sendText(chatId, '❌ Número inválido. Envía un teléfono válido:');
    return;
  }

  // Guardar datos temporalmente en la hoja del usuario
  var userSheet = getUserSheet(chatId, userName);
  storeUserTempData(userSheet, 'phone', phone);

  sendText(chatId, '📧 Perfecto. Ahora envía tu email (debe ser Gmail):');
  userStates[chatId] = 'waiting_email';
}

function handleEmailInput(chatId, userId, email) {
  if (!email.includes('@gmail.com')) {
    sendText(chatId, '❌ Debe ser Gmail. Envía un email válido:');
    return;
  }

  var userSheet = getUserSheet(chatId, getUserNameFromSheet(userId));
  storeUserTempData(userSheet, 'email', email);

  sendText(chatId, '📦 Excelente. Ahora envía tu código de tracking de CubaMax (ej: EDF6V-BPF):');
  userStates[chatId] = 'waiting_tracking';
}

function handleTrackingInput(chatId, userId, trackingCode) {
  if (!isValidTrackingCode(trackingCode)) {
    sendText(chatId, '❌ Código inválido. Formato correcto: ABC12-DEF\nIntenta de nuevo:');
    return;
  }

  var userSheet = getUserSheet(chatId, getUserNameFromSheet(userId));
  storeUserTempData(userSheet, 'tracking', trackingCode);

  sendText(chatId, '⏰ ¿Cada cuánto quieres recibir notificaciones?\n\n*1* - Cada hora\n*2* - Diario\n*3* - Semanal\n\nEnvía el número (1, 2 o 3):');
  userStates[chatId] = 'waiting_frequency';
}

function handleFrequencyInput(chatId, userId, frequency) {
  var frequencies = {
    '1': 'hourly',
    '2': 'daily',
    '3': 'weekly'
  };

  if (!frequencies[frequency]) {
    sendText(chatId, '❌ Opción inválida. Envía 1, 2 o 3:');
    return;
  }

  completeUserRegistration(chatId, userId, frequencies[frequency]);
}

function getUserSheet(chatId, userName) {
  var ss = SpreadsheetApp.openById(SPREADSHEET_ID);
  var sheetName = userName.replace(/\s+/g, '_') + '_' + chatId;
  var sheet = ss.getSheetByName(sheetName);

  if (!sheet) {
    sheet = ss.insertSheet(sheetName);
    sheet.appendRow(['User ID', 'User Name', 'Phone', 'Email', 'Tracking', 'Status', 'Frequency', 'Timestamp']);
  }

  return sheet;
}

function storeUserTempData(sheet, key, value) {
  var timestamp = new Date().toISOString();
  sheet.appendRow(['', '', key, value, '', '', '', timestamp]);
}

function getUserTempData(sheet) {
  var data = sheet.getDataRange().getValues();
  var tempData = {};

  for (var i = 1; i < data.length; i++) {
    if (data[i][2] && data[i][3]) { // Si hay key y value
      tempData[data[i][2]] = data[i][3];
    }
  }

  return tempData;
}

function completeUserRegistration(chatId, userId, frequency) {
  try {
    var userName = getUserNameFromSheet(userId);
    var userSheet = getUserSheet(chatId, userName);
    var tempData = getUserTempData(userSheet);

    // Hacer scraping inicial
    var trackingData = scrapeTrackingData(tempData.tracking);
    var currentStatus = trackingData ? getLatestStatus(trackingData) : 'Sin datos';

    // Guardar en la hoja principal USERS
    var saved = saveUserToMainSheet(userName, userId, tempData.phone, tempData.email, tempData.tracking, currentStatus, frequency);

    if (saved) {
      // Limpiar estado
      userStates[chatId] = 'normal';

      var msg = `✅ *¡Registro completado!*\n\n📦 *Tracking:* ${tempData.tracking}\n📊 *Estado actual:* ${currentStatus}\n⏰ *Notificaciones:* ${getFrequencyText(frequency)}\n\n🔔 Recibirás actualizaciones automáticas cada 20 minutos.`;
      sendText(chatId, msg);

      if (trackingData) {
        sendTrackingUpdate(chatId, trackingData);
      }
    } else {
      sendText(chatId, '❌ Error guardando datos. Intenta /start de nuevo.');
    }

  } catch (error) {
    console.error('Error en registro:', error);
    sendText(chatId, '❌ Error en registro. Intenta /start de nuevo.');
  }
}

function saveUserToMainSheet(userName, userId, phone, email, trackingCode, status, frequency) {
  try {
    var sheet = getOrCreateSheet('USERS');

    // Verificar si ya existe
    if (isExistingUser(sheet, userId)) {
      updateExistingUser(sheet, userId, status, frequency);
      return true;
    } else {
      // Agregar nuevo usuario
      var timestamp = new Date();
      sheet.appendRow([userName, userId, phone, email, trackingCode, timestamp, status, frequency, null, timestamp]);
      console.log(`Usuario ${userName} guardado exitosamente`);
      return true;
    }
  } catch (error) {
    console.error('Error guardando usuario:', error);
    return false;
  }
}

function getOrCreateSheet(sheetName) {
  var ss = SpreadsheetApp.openById(SPREADSHEET_ID);
  var sheet = ss.getSheetByName(sheetName);

  if (!sheet) {
    sheet = ss.insertSheet(sheetName);
    if (sheetName === 'USERS') {
      sheet.appendRow(['USER NAME', 'USER ID', 'USER NUMBER', 'USER MAIL', 'TRACKING NUMBER', 'TIME', 'STATUS', 'FREQUENCY', 'LAST NOTIFICATION', 'LAST CHECK']);
    }
  }

  return sheet;
}

function getUserData(userId) {
  try {
    var sheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName('USERS');
    if (!sheet) return null;

    var data = sheet.getDataRange().getValues();

    for (var i = 1; i < data.length; i++) {
      if (data[i][1] && data[i][1].toString() === userId.toString()) {
        return {
          username: data[i][0],
          userId: data[i][1],
          phone: data[i][2],
          email: data[i][3],
          trackingNumber: data[i][4],
          time: data[i][5],
          status: data[i][6],
          frequency: data[i][7] || 'daily',
          lastNotification: data[i][8],
          lastCheck: data[i][9]
        };
      }
    }
    return null;
  } catch (error) {
    console.error('Error obteniendo usuario:', error);
    return null;
  }
}

function isExistingUser(sheet, userId) {
  var data = sheet.getDataRange().getValues();
  for (var i = 1; i < data.length; i++) {
    if (data[i][1] && data[i][1].toString() === userId.toString()) {
      return true;
    }
  }
  return false;
}

function updateExistingUser(sheet, userId, status, frequency) {
  var data = sheet.getDataRange().getValues();
  for (var i = 1; i < data.length; i++) {
    if (data[i][1] && data[i][1].toString() === userId.toString()) {
      sheet.getRange(i + 1, 7).setValue(status); // STATUS
      sheet.getRange(i + 1, 8).setValue(frequency); // FREQUENCY
      sheet.getRange(i + 1, 10).setValue(new Date()); // LAST CHECK
      return true;
    }
  }
  return false;
}

function getUserNameFromSheet(userId) {
  var user = getUserData(userId);
  return user ? user.username : 'User_' + userId;
}

function scrapeTrackingData(trackingCode) {
  try {
    console.log(`Haciendo scraping para: ${trackingCode}`);

    var url = `https://m.cubamax.com/trk/${trackingCode}`;
    var response = UrlFetchApp.fetch(url, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });

    var html = response.getContentText();
    var $ = Cheerio.load(html);

    var trackingData = {
      trackingCode: trackingCode,
      timestamp: new Date(),
      clientName: '',
      hbl: '',
      events: []
    };

    // Extraer información del cliente
    trackingData.clientName = $('.font-weight-bold.text-uppercase').eq(1).text().trim();
    trackingData.hbl = $('.font-weight-bold.text-uppercase').eq(0).text().replace('HBL: ', '').trim();

    // Extraer eventos de tracking
    $('.d-flex.align-center').each(function(index, element) {
      var statusElement = $(element).find('.font-weight-bold.text-uppercase').first();
      var dateElement = $(element).find('.text-caption').first();
      var descElement = $(element).find('.text-caption').last();

      if (statusElement.length && dateElement.length) {
        var status = statusElement.text().trim();
        var date = dateElement.text().trim();
        var description = descElement.text().trim();
        var isCompleted = !$(element).find('.font-italic').text().includes('pending');

        trackingData.events.push({
          status: status,
          date: date,
          description: description,
          completed: isCompleted
        });
      }
    });

    console.log(`Scraping completado: ${trackingData.events.length} eventos`);
    return trackingData;

  } catch (error) {
    console.error('Error en scraping:', error);
    return null;
  }
}

function getLatestStatus(trackingData) {
  if (!trackingData || !trackingData.events || trackingData.events.length === 0) {
    return 'Sin datos';
  }

  // Buscar el último evento completado
  var completedEvents = trackingData.events.filter(function(event) {
    return event.completed;
  });

  if (completedEvents.length > 0) {
    return completedEvents[completedEvents.length - 1].status;
  }

  // Si no hay eventos completados, retornar el primero
  return trackingData.events[0].status;
}

function isValidTrackingCode(code) {
  var regex = /^[A-Z0-9]{3,}-[A-Z0-9]{2,}$/i;
  return regex.test(code);
}

function getFrequencyText(frequency) {
  var texts = {
    'hourly': 'Cada hora',
    'daily': 'Diario',
    'weekly': 'Semanal'
  };
  return texts[frequency] || 'Diario';
}

/**
 * Maneja entrada de email
 */
function handleEmailInput(chatId, userId, email) {
  if (!email.includes('@gmail.com')) {
    sendMessage(chatId, '❌ Debe ser Gmail. Envía un email válido:');
    return;
  }

  setUserTempData(userId, 'email', email);
  sendMessage(chatId, '📦 Ahora envía tu código de tracking (ej: EDF6V-BPX):');
  setUserState(userId, 'waiting_tracking');
}

/**
 * Maneja entrada de tracking
 */
function handleTrackingInput(chatId, userId, trackingCode) {
  if (!isValidTrackingCode(trackingCode)) {
    sendMessage(chatId, '❌ Código inválido. Formato: ABC12-DEF\nIntenta de nuevo:');
    return;
  }

  setUserTempData(userId, 'trackingCode', trackingCode);
  sendMessage(chatId, '⏰ ¿Cada cuánto quieres recibir notificaciones?\n\n1 - Cada hora\n2 - Diario\n3 - Semanal\n\nEnvía el número (1, 2 o 3):');
  setUserState(userId, 'waiting_frequency');
}

/**
 * Maneja entrada de frecuencia
 */
function handleFrequencyInput(chatId, userId, frequency) {
  const frequencies = {
    '1': 'hourly',
    '2': 'daily',
    '3': 'weekly'
  };

  if (!frequencies[frequency]) {
    sendMessage(chatId, '❌ Opción inválida. Envía 1, 2 o 3:');
    return;
  }

  completeRegistration(chatId, userId, frequencies[frequency]);
}

/**
 * Completa el registro del usuario
 */
function completeRegistration(chatId, userId, frequency) {
  try {
    const tempData = getUserTempData(userId);

    // Hacer scraping inicial
    const trackingData = scrapeTrackingData(tempData.trackingCode);
    const currentStatus = trackingData ? getLatestStatus(trackingData) : 'Sin datos';

    // Guardar usuario
    const saved = saveUser(
      tempData.username,
      userId,
      tempData.phone,
      tempData.email,
      tempData.trackingCode,
      currentStatus,
      frequency
    );

    if (saved) {
      clearUserData(userId);

      const msg = `✅ ¡Registro completado!\n\n📦 Tracking: ${tempData.trackingCode}\n📊 Estado actual: ${currentStatus}\n⏰ Notificaciones: ${getFrequencyText(frequency)}\n\n🔔 Recibirás actualizaciones automáticas.`;
      sendMessage(chatId, msg);

      if (trackingData) {
        sendTrackingUpdate(chatId, trackingData);
      }
    } else {
      sendMessage(chatId, '❌ Error guardando datos. Intenta /start de nuevo.');
    }

  } catch (error) {
    console.error('Error en registro:', error);
    sendMessage(chatId, '❌ Error en registro. Intenta /start de nuevo.');
  }
}

/**
 * FUNCIONES DE BASE DE DATOS
 */

/**
 * Obtiene datos del usuario
 */
function getUserData(userId) {
  try {
    const sheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName('USERS');
    if (!sheet) return null;

    const data = sheet.getDataRange().getValues();

    for (let i = 1; i < data.length; i++) {
      if (data[i][1] && data[i][1].toString() === userId.toString()) {
        return {
          username: data[i][0],
          userId: data[i][1],
          phone: data[i][2],
          email: data[i][3],
          trackingNumber: data[i][4],
          time: data[i][5],
          status: data[i][6],
          frequency: data[i][7] || 'daily',
          lastNotification: data[i][8],
          lastCheck: data[i][9]
        };
      }
    }
    return null;
  } catch (error) {
    console.error('Error obteniendo usuario:', error);
    return null;
  }
}

/**
 * Guarda usuario en la base de datos
 */
function saveUser(username, userId, phone, email, trackingCode, status, frequency) {
  try {
    console.log(`Guardando usuario: ${username} (${userId})`);

    const sheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName('USERS');
    if (!sheet) {
      console.error('Hoja USERS no existe');
      return false;
    }

    const timestamp = new Date();

    // Verificar si el usuario ya existe
    const existingUser = getUserData(userId);
    if (existingUser) {
      // Actualizar usuario existente
      updateUserStatus(userId, status, frequency);
      return true;
    } else {
      // Agregar nuevo usuario
      const newRow = [
        username,           // A - USER NAME
        userId,             // B - USER ID
        phone,              // C - USER NUMBER
        email,              // D - USER MAIL
        trackingCode,       // E - TRACKING NUMBER
        timestamp,          // F - TIME
        status,             // G - STATUS
        frequency,          // H - FREQUENCY
        null,               // I - LAST NOTIFICATION
        timestamp           // J - LAST CHECK
      ];

      sheet.appendRow(newRow);
      console.log(`Usuario ${username} guardado exitosamente`);
      return true;
    }
  } catch (error) {
    console.error('Error guardando usuario:', error);
    return false;
  }
}

/**
 * Actualiza el status del usuario
 */
function updateUserStatus(userId, newStatus, frequency = null) {
  try {
    const sheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName('USERS');
    if (!sheet) return false;

    const data = sheet.getDataRange().getValues();

    for (let i = 1; i < data.length; i++) {
      if (data[i][1] && data[i][1].toString() === userId.toString()) {
        // Actualizar status (columna G)
        sheet.getRange(i + 1, 7).setValue(newStatus);

        // Actualizar last check (columna J)
        sheet.getRange(i + 1, 10).setValue(new Date());

        // Actualizar frequency si se proporciona
        if (frequency) {
          sheet.getRange(i + 1, 8).setValue(frequency);
        }

        console.log(`Status actualizado para usuario ${userId}: ${newStatus}`);
        return true;
      }
    }
    return false;
  } catch (error) {
    console.error('Error actualizando status:', error);
    return false;
  }
}

/**
 * Actualiza la última notificación del usuario
 */
function updateLastNotification(userId) {
  try {
    const sheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName('USERS');
    if (!sheet) return false;

    const data = sheet.getDataRange().getValues();

    for (let i = 1; i < data.length; i++) {
      if (data[i][1] && data[i][1].toString() === userId.toString()) {
        sheet.getRange(i + 1, 9).setValue(new Date()); // Columna I - LAST NOTIFICATION
        return true;
      }
    }
    return false;
  } catch (error) {
    console.error('Error actualizando última notificación:', error);
    return false;
  }
}

/**
 * FUNCIONES DE SCRAPING
 */

/**
 * Realiza scraping de los datos de tracking
 */
function scrapeTrackingData(trackingCode) {
  try {
    console.log(`Haciendo scraping para: ${trackingCode}`);

    const url = `https://m.cubamax.com/trk/${trackingCode}`;
    const response = UrlFetchApp.fetch(url, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });

    const html = response.getContentText();
    const $ = Cheerio.load(html);

    const trackingData = {
      trackingCode: trackingCode,
      timestamp: new Date(),
      clientName: '',
      hbl: '',
      events: []
    };

    // Extraer información del cliente
    trackingData.clientName = $('.font-weight-bold.text-uppercase').eq(1).text().trim();
    trackingData.hbl = $('.font-weight-bold.text-uppercase').eq(0).text().replace('HBL: ', '').trim();

    // Extraer eventos de tracking
    $('.d-flex.align-center').each((index, element) => {
      const statusElement = $(element).find('.font-weight-bold.text-uppercase').first();
      const dateElement = $(element).find('.text-caption').first();
      const descElement = $(element).find('.text-caption').last();

      if (statusElement.length && dateElement.length) {
        const status = statusElement.text().trim();
        const date = dateElement.text().trim();
        const description = descElement.text().trim();
        const isCompleted = !$(element).find('.font-italic').text().includes('pending');

        trackingData.events.push({
          status: status,
          date: date,
          description: description,
          completed: isCompleted
        });
      }
    });

    console.log(`Scraping completado: ${trackingData.events.length} eventos`);
    return trackingData;

  } catch (error) {
    console.error('Error en scraping:', error);
    return null;
  }
}

/**
 * Obtiene el último status del tracking
 */
function getLatestStatus(trackingData) {
  if (!trackingData || !trackingData.events || trackingData.events.length === 0) {
    return 'Sin datos';
  }

  // Buscar el último evento completado
  const completedEvents = trackingData.events.filter(event => event.completed);
  if (completedEvents.length > 0) {
    return completedEvents[completedEvents.length - 1].status;
  }

  // Si no hay eventos completados, retornar el primero
  return trackingData.events[0].status;
}

/**
 * FUNCIONES DE UTILIDAD
 */

/**
 * Valida código de tracking
 */
function isValidTrackingCode(code) {
  const regex = /^[A-Z0-9]{3,}-[A-Z0-9]{2,}$/i;
  return regex.test(code);
}

/**
 * Obtiene texto de frecuencia
 */
function getFrequencyText(frequency) {
  const texts = {
    'hourly': 'Cada hora',
    'daily': 'Diario',
    'weekly': 'Semanal'
  };
  return texts[frequency] || 'Diario';
}

/**
 * Obtiene token del bot
 */
function getBotToken() {
  const sheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName('BOT-TOKEN');
  if (!sheet) {
    console.error('Hoja BOT-TOKEN no existe');
    return null;
  }
  return sheet.getRange('A2').getValue();
}

/**
 * Envía mensaje de Telegram
 */
function sendMessage(chatId, text, parseMode = null) {
  try {
    const token = getBotToken();
    if (!token) {
      console.error('Token del bot no configurado');
      return;
    }

    const url = `${TELEGRAM_API_URL}${token}/sendMessage`;
    const payload = {
      chat_id: chatId,
      text: text
    };

    if (parseMode) {
      payload.parse_mode = parseMode;
    }

    const options = {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      payload: JSON.stringify(payload)
    };

    UrlFetchApp.fetch(url, options);
  } catch (error) {
    console.error('Error enviando mensaje:', error);
  }
}

/**
 * FUNCIONES DE ESTADOS TEMPORALES
 */

function setUserState(userId, state) {
  userStates[userId] = state;
}

function getUserState(userId) {
  return userStates[userId] || 'none';
}

function setUserTempData(userId, key, value) {
  if (!userTempData[userId]) {
    userTempData[userId] = {};
  }
  userTempData[userId][key] = value;
}

function getUserTempData(userId) {
  return userTempData[userId] || {};
}

function clearUserData(userId) {
  delete userStates[userId];
  delete userTempData[userId];
}

/**
 * FUNCIONES DE COMANDOS
 */

/**
 * Muestra el status actual del usuario
 */
function showUserStatus(chatId, userId) {
  const user = getUserData(userId);

  if (!user) {
    sendMessage(chatId, '❌ No estás registrado. Usa /start para registrarte.');
    return;
  }

  // Hacer scraping actualizado
  const trackingData = scrapeTrackingData(user.trackingNumber);

  if (trackingData) {
    const newStatus = getLatestStatus(trackingData);

    // Actualizar status en la base de datos
    updateUserStatus(userId, newStatus);

    // Enviar información actualizada
    sendTrackingUpdate(chatId, trackingData);
  } else {
    sendMessage(chatId, `📦 Tracking: ${user.trackingNumber}\n📊 Último estado conocido: ${user.status}\n\n❌ No se pudo obtener información actualizada.`);
  }
}

/**
 * Muestra configuraciones del usuario
 */
function showSettings(chatId, userId) {
  const user = getUserData(userId);

  if (!user) {
    sendMessage(chatId, '❌ No estás registrado. Usa /start para registrarte.');
    return;
  }

  const msg = `⚙️ *Configuración Actual*\n\n📦 Tracking: \`${user.trackingNumber}\`\n📊 Estado: ${user.status}\n⏰ Notificaciones: ${getFrequencyText(user.frequency)}\n\n_Para cambiar la configuración, contacta al administrador._`;

  sendMessage(chatId, msg, 'Markdown');
}

/**
 * Envía actualización de tracking formateada
 */
function sendTrackingUpdate(chatId, trackingData) {
  if (!trackingData) return;

  let message = `📦 *Estado del Envío*\n\n`;
  message += `🔢 Tracking: \`${trackingData.trackingCode}\`\n`;

  if (trackingData.clientName) {
    message += `👤 Cliente: ${trackingData.clientName}\n`;
  }

  if (trackingData.hbl) {
    message += `📋 HBL: ${trackingData.hbl}\n`;
  }

  message += `📅 Actualizado: ${trackingData.timestamp.toLocaleString('es-ES')}\n\n`;
  message += `*🚚 Eventos del Envío:*\n\n`;

  trackingData.events.forEach((event, index) => {
    const icon = event.completed ? '✅' : '⏳';
    message += `${icon} *${event.status}*\n`;

    if (event.date && event.date !== 'pending') {
      message += `📅 ${event.date}\n`;
    }

    if (event.description) {
      message += `📝 ${event.description}\n`;
    }

    message += '\n';
  });

  sendMessage(chatId, message, 'Markdown');
}

/**
 * FUNCIONES DE NOTIFICACIONES AUTOMÁTICAS
 */

/**
 * Función que se ejecuta cada 20 minutos para verificar actualizaciones
 */
function checkAllTrackingUpdates() {
  try {
    console.log('Iniciando verificación automática de tracking...');

    const sheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName('USERS');
    if (!sheet) {
      console.error('Hoja USERS no existe');
      return;
    }

    const data = sheet.getDataRange().getValues();
    let updatedCount = 0;

    for (let i = 1; i < data.length; i++) {
      const user = {
        username: data[i][0],
        userId: data[i][1],
        phone: data[i][2],
        email: data[i][3],
        trackingNumber: data[i][4],
        time: data[i][5],
        status: data[i][6],
        frequency: data[i][7] || 'daily',
        lastNotification: data[i][8],
        lastCheck: data[i][9]
      };

      if (user.trackingNumber) {
        // Hacer scraping
        const trackingData = scrapeTrackingData(user.trackingNumber);

        if (trackingData) {
          const newStatus = getLatestStatus(trackingData);

          // Si el status cambió, actualizar en la base de datos
          if (newStatus !== user.status) {
            updateUserStatus(user.userId, newStatus);
            console.log(`Status actualizado para ${user.username}: ${user.status} → ${newStatus}`);
            updatedCount++;

            // Verificar si debe enviar notificación
            if (shouldSendNotification(user)) {
              sendNotificationToUser(user.userId, trackingData, newStatus);
              updateLastNotification(user.userId);
            }
          }
        }

        // Pequeña pausa para evitar rate limiting
        Utilities.sleep(1000);
      }
    }

    console.log(`Verificación completada. ${updatedCount} usuarios actualizados.`);

  } catch (error) {
    console.error('Error en verificación automática:', error);
  }
}

/**
 * Determina si debe enviar notificación al usuario
 */
function shouldSendNotification(user) {
  if (!user.lastNotification) return true;

  const now = new Date();
  const lastNotification = new Date(user.lastNotification);
  const hoursDiff = (now - lastNotification) / (1000 * 60 * 60);

  switch (user.frequency) {
    case 'hourly':
      return hoursDiff >= 1;
    case 'daily':
      return hoursDiff >= 24;
    case 'weekly':
      return hoursDiff >= 168; // 24 * 7
    default:
      return hoursDiff >= 24;
  }
}

/**
 * Envía notificación a un usuario específico
 */
function sendNotificationToUser(userId, trackingData, newStatus) {
  try {
    const chatId = userId; // Asumiendo que chatId = userId

    let message = `🔔 *Actualización de tu Envío*\n\n`;
    message += `📦 Tracking: \`${trackingData.trackingCode}\`\n`;
    message += `📊 *Nuevo Estado: ${newStatus}*\n\n`;

    // Mostrar último evento completado
    const completedEvents = trackingData.events.filter(event => event.completed);
    if (completedEvents.length > 0) {
      const lastEvent = completedEvents[completedEvents.length - 1];
      message += `✅ ${lastEvent.status}\n`;
      if (lastEvent.date && lastEvent.date !== 'pending') {
        message += `📅 ${lastEvent.date}\n`;
      }
      if (lastEvent.description) {
        message += `📝 ${lastEvent.description}\n`;
      }
    }

    message += `\n📅 ${new Date().toLocaleString('es-ES')}`;

    sendMessage(chatId, message, 'Markdown');
    console.log(`Notificación enviada a usuario ${userId}`);

  } catch (error) {
    console.error(`Error enviando notificación a usuario ${userId}:`, error);
  }
}

/**
 * FUNCIONES DE CONFIGURACIÓN
 */

/**
 * Configura el webhook de Telegram
 */
function setWebhook() {
  const token = getBotToken();
  if (!token) {
    console.error('Token no configurado');
    return;
  }

  const webAppUrl = 'TU_WEB_APP_URL_AQUI'; // Reemplazar con tu URL

  const url = `${TELEGRAM_API_URL}${token}/setWebhook`;
  const payload = { url: webAppUrl };

  const options = {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    payload: JSON.stringify(payload)
  };

  try {
    const response = UrlFetchApp.fetch(url, options);
    console.log('Webhook configurado:', response.getContentText());
  } catch (error) {
    console.error('Error configurando webhook:', error);
  }
}

/**
 * Configura triggers automáticos
 */
function setupTriggers() {
  // Eliminar triggers existentes
  const triggers = ScriptApp.getProjectTriggers();
  triggers.forEach(trigger => {
    if (trigger.getHandlerFunction() === 'checkAllTrackingUpdates') {
      ScriptApp.deleteTrigger(trigger);
    }
  });

  // Crear trigger cada 20 minutos
  ScriptApp.newTrigger('checkAllTrackingUpdates')
    .timeBased()
    .everyMinutes(20)
    .create();

  console.log('Trigger configurado para ejecutar cada 20 minutos');
}

/**
 * FUNCIONES DE PRUEBA Y CONFIGURACIÓN
 */

/**
 * Función de prueba completa
 */
function testBot() {
  console.log('=== PRUEBA COMPLETA DEL BOT ===');

  // 1. Verificar acceso a Google Sheets
  try {
    const sheet = SpreadsheetApp.openById(SPREADSHEET_ID);
    console.log('✅ Acceso a Google Sheets: OK');
    console.log(`Nombre de la hoja: ${sheet.getName()}`);
  } catch (error) {
    console.error('❌ Error accediendo a Google Sheets:', error);
    return;
  }

  // 2. Verificar hoja USERS
  try {
    const usersSheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName('USERS');
    if (usersSheet) {
      console.log('✅ Hoja USERS: OK');
      console.log(`Filas en USERS: ${usersSheet.getLastRow()}`);
    } else {
      console.error('❌ Hoja USERS no existe');
    }
  } catch (error) {
    console.error('❌ Error verificando hoja USERS:', error);
  }

  // 3. Verificar token del bot
  try {
    const token = getBotToken();
    if (token && token.length > 10) {
      console.log('✅ Token del bot: OK');
    } else {
      console.error('❌ Token del bot no configurado');
    }
  } catch (error) {
    console.error('❌ Error obteniendo token:', error);
  }

  // 4. Probar scraping
  try {
    console.log('Probando scraping...');
    const trackingData = scrapeTrackingData('EDF6V-BPF');
    if (trackingData && trackingData.events.length > 0) {
      console.log('✅ Scraping: OK');
      console.log(`Eventos encontrados: ${trackingData.events.length}`);
      console.log(`Cliente: ${trackingData.clientName}`);
    } else {
      console.error('❌ Scraping falló o sin datos');
    }
  } catch (error) {
    console.error('❌ Error en scraping:', error);
  }

  console.log('=== FIN DE PRUEBA ===');
}

/**
 * Función para crear la estructura inicial de la hoja
 */
function setupInitialSheet() {
  try {
    console.log('Configurando estructura inicial...');

    const spreadsheet = SpreadsheetApp.openById(SPREADSHEET_ID);

    // Crear hoja USERS si no existe
    let usersSheet = spreadsheet.getSheetByName('USERS');
    if (!usersSheet) {
      usersSheet = spreadsheet.insertSheet('USERS');
      console.log('Hoja USERS creada');
    }

    // Configurar encabezados en USERS
    const headers = [
      'USER NAME',      // A
      'USER ID',        // B
      'USER NUMBER',    // C
      'USER MAIL',      // D
      'TRACKING NUMBER',// E
      'TIME',           // F
      'STATUS',         // G
      'FREQUENCY',      // H
      'LAST NOTIFICATION', // I
      'LAST CHECK'      // J
    ];

    usersSheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    usersSheet.getRange(1, 1, 1, headers.length).setFontWeight('bold');

    // Crear hoja BOT-TOKEN si no existe
    let tokenSheet = spreadsheet.getSheetByName('BOT-TOKEN');
    if (!tokenSheet) {
      tokenSheet = spreadsheet.insertSheet('BOT-TOKEN');
      tokenSheet.getRange('A1').setValue('BOT TOKEN');
      tokenSheet.getRange('A1').setFontWeight('bold');
      tokenSheet.getRange('A2').setValue('PEGA_TU_TOKEN_AQUI');
      console.log('Hoja BOT-TOKEN creada');
    }

    console.log('✅ Estructura inicial configurada');

  } catch (error) {
    console.error('Error configurando estructura inicial:', error);
  }
}

/**
 * Función para verificar usuarios registrados
 */
function verifyUsers() {
  try {
    const sheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName('USERS');
    if (!sheet) {
      console.log('Hoja USERS no existe');
      return;
    }

    const data = sheet.getDataRange().getValues();
    console.log(`Total de filas: ${data.length}`);

    if (data.length > 1) {
      console.log('Usuarios registrados:');
      for (let i = 1; i < data.length; i++) {
        const user = data[i];
        console.log(`${i}. ${user[0]} (${user[1]}) - Tracking: ${user[4]} - Status: ${user[6]}`);
      }
    } else {
      console.log('No hay usuarios registrados');
    }

  } catch (error) {
    console.error('Error verificando usuarios:', error);
  }
}

/**
 * Función de configuración completa
 */
function setupCompleteBot() {
  console.log('🚀 CONFIGURACIÓN COMPLETA DEL BOT');

  // 1. Configurar estructura inicial
  setupInitialSheet();

  // 2. Configurar triggers
  setupTriggers();

  // 3. Probar bot
  testBot();

  // 4. Verificar usuarios
  verifyUsers();

  console.log('✅ Configuración completa terminada');
  console.log('📝 Próximos pasos:');
  console.log('1. Configura tu token en la hoja BOT-TOKEN');
  console.log('2. Configura el webhook con setWebhook()');
  console.log('3. Prueba enviando /start a tu bot');
}

function showUserStatus(chatId, userId) {
  var user = getUserData(userId);

  if (!user) {
    sendText(chatId, '❌ No estás registrado. Usa /start para registrarte.');
    return;
  }

  // Hacer scraping actualizado
  var trackingData = scrapeTrackingData(user.trackingNumber);

  if (trackingData) {
    var newStatus = getLatestStatus(trackingData);

    // Actualizar status en la base de datos si cambió
    if (newStatus !== user.status) {
      updateUserStatus(userId, newStatus);
    }

    // Enviar información actualizada
    sendTrackingUpdate(chatId, trackingData);
  } else {
    sendText(chatId, `📦 *Tracking:* ${user.trackingNumber}\n📊 *Último estado conocido:* ${user.status}\n\n❌ No se pudo obtener información actualizada.`);
  }
}

function sendTrackingUpdate(chatId, trackingData) {
  if (!trackingData) return;

  var message = `📦 *Estado del Envío*\n\n`;
  message += `🔢 *Tracking:* \`${trackingData.trackingCode}\`\n`;

  if (trackingData.clientName) {
    message += `👤 *Cliente:* ${trackingData.clientName}\n`;
  }

  if (trackingData.hbl) {
    message += `📋 *HBL:* ${trackingData.hbl}\n`;
  }

  message += `📅 *Actualizado:* ${trackingData.timestamp.toLocaleString('es-ES')}\n\n`;
  message += `*🚚 Eventos del Envío:*\n\n`;

  for (var i = 0; i < trackingData.events.length; i++) {
    var event = trackingData.events[i];
    var icon = event.completed ? '✅' : '⏳';
    message += `${icon} *${event.status}*\n`;

    if (event.date && event.date !== 'pending') {
      message += `📅 ${event.date}\n`;
    }

    if (event.description) {
      message += `📝 ${event.description}\n`;
    }

    message += '\n';
  }

  sendText(chatId, message);
}

function updateUserStatus(userId, newStatus) {
  try {
    var sheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName('USERS');
    if (!sheet) return false;

    var data = sheet.getDataRange().getValues();

    for (var i = 1; i < data.length; i++) {
      if (data[i][1] && data[i][1].toString() === userId.toString()) {
        sheet.getRange(i + 1, 7).setValue(newStatus); // STATUS
        sheet.getRange(i + 1, 10).setValue(new Date()); // LAST CHECK
        console.log(`Status actualizado para usuario ${userId}: ${newStatus}`);
        return true;
      }
    }
    return false;
  } catch (error) {
    console.error('Error actualizando status:', error);
    return false;
  }
}

function checkAllTrackingUpdates() {
  try {
    console.log('Iniciando verificación automática de tracking...');

    var sheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName('USERS');
    if (!sheet) {
      console.error('Hoja USERS no existe');
      return;
    }

    var data = sheet.getDataRange().getValues();
    var updatedCount = 0;

    for (var i = 1; i < data.length; i++) {
      var user = {
        username: data[i][0],
        userId: data[i][1],
        phone: data[i][2],
        email: data[i][3],
        trackingNumber: data[i][4],
        time: data[i][5],
        status: data[i][6],
        frequency: data[i][7] || 'daily',
        lastNotification: data[i][8],
        lastCheck: data[i][9]
      };

      if (user.trackingNumber) {
        // Hacer scraping
        var trackingData = scrapeTrackingData(user.trackingNumber);

        if (trackingData) {
          var newStatus = getLatestStatus(trackingData);

          // Si el status cambió, actualizar en la base de datos
          if (newStatus !== user.status) {
            updateUserStatus(user.userId, newStatus);
            console.log(`Status actualizado para ${user.username}: ${user.status} → ${newStatus}`);
            updatedCount++;

            // Verificar si debe enviar notificación
            if (shouldSendNotification(user)) {
              sendNotificationToUser(user.userId, trackingData, newStatus);
              updateLastNotification(user.userId);
            }
          }
        }

        // Pequeña pausa para evitar rate limiting
        Utilities.sleep(1000);
      }
    }

    console.log(`Verificación completada. ${updatedCount} usuarios actualizados.`);

  } catch (error) {
    console.error('Error en verificación automática:', error);
  }
}

function shouldSendNotification(user) {
  if (!user.lastNotification) return true;

  var now = new Date();
  var lastNotification = new Date(user.lastNotification);
  var hoursDiff = (now - lastNotification) / (1000 * 60 * 60);

  switch (user.frequency) {
    case 'hourly':
      return hoursDiff >= 1;
    case 'daily':
      return hoursDiff >= 24;
    case 'weekly':
      return hoursDiff >= 168; // 24 * 7
    default:
      return hoursDiff >= 24;
  }
}

function sendNotificationToUser(userId, trackingData, newStatus) {
  try {
    var chatId = userId; // Asumiendo que chatId = userId

    var message = `🔔 *Actualización de tu Envío*\n\n`;
    message += `📦 *Tracking:* \`${trackingData.trackingCode}\`\n`;
    message += `📊 *Nuevo Estado:* ${newStatus}\n\n`;

    // Mostrar último evento completado
    var completedEvents = trackingData.events.filter(function(event) {
      return event.completed;
    });

    if (completedEvents.length > 0) {
      var lastEvent = completedEvents[completedEvents.length - 1];
      message += `✅ ${lastEvent.status}\n`;
      if (lastEvent.date && lastEvent.date !== 'pending') {
        message += `📅 ${lastEvent.date}\n`;
      }
      if (lastEvent.description) {
        message += `📝 ${lastEvent.description}\n`;
      }
    }

    message += `\n📅 ${new Date().toLocaleString('es-ES')}`;

    sendText(chatId, message);
    console.log(`Notificación enviada a usuario ${userId}`);

  } catch (error) {
    console.error(`Error enviando notificación a usuario ${userId}:`, error);
  }
}

function updateLastNotification(userId) {
  try {
    var sheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName('USERS');
    if (!sheet) return false;

    var data = sheet.getDataRange().getValues();

    for (var i = 1; i < data.length; i++) {
      if (data[i][1] && data[i][1].toString() === userId.toString()) {
        sheet.getRange(i + 1, 9).setValue(new Date()); // LAST NOTIFICATION
        return true;
      }
    }
    return false;
  } catch (error) {
    console.error('Error actualizando última notificación:', error);
    return false;
  }
}

function setupTriggers() {
  // Eliminar triggers existentes
  var triggers = ScriptApp.getProjectTriggers();
  for (var i = 0; i < triggers.length; i++) {
    if (triggers[i].getHandlerFunction() === 'checkAllTrackingUpdates') {
      ScriptApp.deleteTrigger(triggers[i]);
    }
  }

  // Crear trigger cada 20 minutos
  ScriptApp.newTrigger('checkAllTrackingUpdates')
    .timeBased()
    .everyMinutes(20)
    .create();

  console.log('Trigger configurado para ejecutar cada 20 minutos');
}

function setWebhook() {
  var token = getBotToken();
  if (!token) {
    console.error('Token no configurado');
    return;
  }

  var webAppUrl = 'TU_WEB_APP_URL_AQUI'; // Reemplazar con tu URL

  var url = `https://api.telegram.org/bot${token}/setWebhook`;
  var payload = { url: webAppUrl };

  var options = {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    payload: JSON.stringify(payload)
  };

  try {
    var response = UrlFetchApp.fetch(url, options);
    console.log('Webhook configurado:', response.getContentText());
  } catch (error) {
    console.error('Error configurando webhook:', error);
  }
}