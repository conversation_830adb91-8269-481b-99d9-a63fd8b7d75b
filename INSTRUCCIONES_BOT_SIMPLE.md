# 🤖 CubaMax Tracker Bot - Versión Simplificada

## ✅ **CÓDIGO COMPLETAMENTE NUEVO Y FUNCIONAL**

He creado un bot desde cero que es simple, funcional y sin bucles infinitos.

## 🎯 **CARACTERÍSTICAS PRINCIPALES**

### ✅ **Una Sola Hoja de Cálculo**
- ID: `1ELC6g_6GPXzI36MUoy0tiiYHQy0vsiU9In3LKubsHvc`
- Estructura simple y clara

### ✅ **Estructura de Datos**
| A | B | C | D | E | F | G | H | I | J |
|---|---|---|---|---|---|---|---|---|---|
| USER NAME | USER ID | USER NUMBER | USER MAIL | TRACKING NUMBER | TIME | STATUS | FREQUENCY | LAST NOTIFICATION | LAST CHECK |

### ✅ **Funcionalidades**
- ✅ Registro completo de usuarios
- ✅ Scraping automático de CubaMax
- ✅ Actualización de status cada 20 minutos
- ✅ Notificaciones personalizadas (hora/día/semana)
- ✅ Comandos simples y efectivos

---

## 🚀 **CONFIGURACIÓN RÁPIDA**

### **1. Ejecutar Configuración Inicial**
```javascript
setupCompleteBot();
```

**Esto creará:**
- Hoja USERS con encabezados correctos
- Hoja BOT-TOKEN para el token
- Trigger automático cada 20 minutos
- Prueba completa del sistema

### **2. Configurar Token**
1. Ve a tu Google Sheet
2. Hoja "BOT-TOKEN"
3. En celda A2, pega tu token del bot

### **3. Habilitar Cheerio**
1. Google Apps Script → Bibliotecas
2. ID: `1ReeQ6WO8kKNxoaA_O0XEQ589cIrRvEBA9qcWpNqdOP17i47u6N9M5Xh0`
3. Versión más reciente → Guardar

### **4. Configurar Webhook**
1. Apps Script → Implementar → Nueva implementación
2. Tipo: Aplicación web
3. Copia la URL
4. En el código, busca `setWebhook()` y pega tu URL
5. Ejecuta `setWebhook()`

### **5. Probar**
```javascript
testBot();
```

---

## 🤖 **FUNCIONAMIENTO DEL BOT**

### **Registro de Usuario Nuevo:**
1. `/start` → "Envía tu teléfono"
2. Usuario envía teléfono → "Envía tu email Gmail"
3. Usuario envía email → "Envía tu código de tracking"
4. Usuario envía tracking → "Elige frecuencia: 1-Hora, 2-Día, 3-Semana"
5. Usuario elige → ✅ Registro completo + Estado del envío

### **Usuario Existente:**
1. `/start` → Muestra tracking actual y comandos
2. `/status` → Estado actualizado del envío
3. `/settings` → Configuración actual

---

## 🔄 **SISTEMA AUTOMÁTICO**

### **Cada 20 Minutos:**
1. Bot revisa todos los usuarios registrados
2. Hace scraping de cada tracking
3. Compara con status anterior
4. Si cambió → Actualiza en Google Sheets
5. Si es hora de notificar → Envía mensaje al usuario

### **Notificaciones Inteligentes:**
- **Cada hora:** Notifica cada hora si hay cambios
- **Diario:** Notifica una vez al día
- **Semanal:** Notifica una vez a la semana

---

## 📊 **DATOS EN GOOGLE SHEETS**

### **Ejemplo de Usuario Registrado:**
| USER NAME | USER ID | USER NUMBER | USER MAIL | TRACKING NUMBER | TIME | STATUS | FREQUENCY | LAST NOTIFICATION | LAST CHECK |
|-----------|---------|-------------|-----------|-----------------|------|--------|-----------|-------------------|------------|
| juan123 | 123456789 | +1234567890 | <EMAIL> | EDF6V-BPF | 2025-07-13... | Shipped | daily | 2025-07-13... | 2025-07-13... |

### **Columnas Importantes:**
- **STATUS:** Estado actual del envío (se actualiza automáticamente)
- **FREQUENCY:** Frecuencia de notificaciones del usuario
- **LAST NOTIFICATION:** Última vez que se le notificó
- **LAST CHECK:** Última vez que se verificó su tracking

---

## 🧪 **FUNCIONES DE PRUEBA**

### **Probar Todo:**
```javascript
testBot();
```

### **Ver Usuarios Registrados:**
```javascript
verifyUsers();
```

### **Probar Scraping:**
```javascript
const data = scrapeTrackingData('EDF6V-BPF');
console.log(data);
```

### **Verificar Actualizaciones:**
```javascript
checkAllTrackingUpdates();
```

---

## 🎯 **COMANDOS DEL BOT**

- `/start` - Registrarse o ver menú principal
- `/status` - Ver estado actual del envío
- `/settings` - Ver configuración actual

---

## 🔧 **SOLUCIÓN DE PROBLEMAS**

### **Bot no responde:**
```javascript
testBot(); // Ver qué falla
```

### **No guarda usuarios:**
```javascript
verifyUsers(); // Ver usuarios registrados
```

### **Scraping no funciona:**
```javascript
const data = scrapeTrackingData('EDF6V-BPF');
console.log(data); // Probar scraping
```

### **Notificaciones no llegan:**
```javascript
checkAllTrackingUpdates(); // Forzar verificación
```

---

## ✅ **VENTAJAS DE ESTA VERSIÓN**

1. **Simple:** Una sola hoja, estructura clara
2. **Funcional:** Sin bucles infinitos, guardado garantizado
3. **Automático:** Verificación cada 20 minutos
4. **Inteligente:** Notificaciones personalizadas
5. **Completo:** Scraping real de CubaMax
6. **Mantenible:** Código limpio y documentado

---

## 🚀 **EJECUTAR AHORA**

```javascript
// Configurar todo de una vez:
setupCompleteBot();

// Después configurar token y webhook manualmente
// Luego probar:
testBot();
```

**¡Tu bot estará funcionando perfectamente en minutos!** 🎉

---

## 📈 **PRÓXIMAS MEJORAS POSIBLES**

- Múltiples trackings por usuario
- Notificaciones por email
- Estadísticas de envíos
- Integración con otras paqueterías

**El bot está listo para usar y expandir según tus necesidades.** 🚀
