# 📦 CubaMax Tracker Bot - Instrucciones Simples

## ✅ **CÓDIGO LISTO PARA USAR**

El código está completamente configurado con tus IDs de Google Sheets:
- **Hoja Principal:** `1ELC6g_6GPXzI36MUoy0tiiYHQy0vsiU9In3LKubsHvc`
- **Hoja de Usuarios:** `1wQhu1-uF7fK6q-7yVt-vwP1DewpTv_BpiA6r-eG7wEk`

## 🚀 **PASOS PARA ACTIVAR EL BOT**

### 1. **Configurar Google Sheets**

**Ho<PERSON> Principal (1ELC6g_6GPXzI36MUoy0tiiYHQy0vsiU9In3LKubsHvc):**

**Hoja: BOT-TOKEN**
- A1: "BOT TOKEN"
- A2: Tu token del bot (ej: `123456789:ABC-DEF...`)

**Hoja: USERS**
- A1: "USER NAME"
- B1: "USER ID" 
- C1: "USER NUMBER"
- D1: "USER MAIL"
- E1: "TRACKING NUMBER"
- F1: "TIME"

**Hoja: TIME**
- A1: "USER ID"
- B1: "ENTRY TIME"

**Hoja: USER DB**
- A1: "USER SHEET URL"
- A2: (Déjala vacía, no se usa en esta versión)

### 2. **Configurar Google Apps Script**

1. Ve a [script.google.com](https://script.google.com)
2. Crea nuevo proyecto
3. Copia TODO el código de `CUBAMAXTRACKER.gs`
4. Habilita biblioteca Cheerio:
   - Bibliotecas → Agregar biblioteca
   - ID: `1ReeQ6WO8kKNxoaA_O0XEQ589cIrRvEBA9qcWpNqdOP17i47u6N9M5Xh0`
   - Versión más reciente → Guardar

### 3. **Configurar Webhook**

1. En Apps Script: Implementar → Nueva implementación
2. Tipo: Aplicación web
3. Ejecutar como: Yo
4. Acceso: Cualquier persona
5. Copia la URL generada
6. En el código, busca `setWebhook()` y reemplaza `TU_WEB_APP_URL_AQUI` con tu URL
7. Ejecuta la función `setWebhook()`

### 4. **Probar el Bot**

Ejecuta la función `testBot()` en Apps Script para verificar que todo funciona.

## 🤖 **FUNCIONAMIENTO DEL BOT**

### **Usuario Nuevo:**
1. Envía `/start`
2. Bot pide teléfono
3. Usuario envía teléfono
4. Bot pide email Gmail
5. Usuario envía email
6. Bot pide código de tracking
7. Usuario envía código (ej: EDF6V-BPF)
8. Bot confirma registro y muestra estado del envío

### **Usuario Existente:**
1. Envía `/start`
2. Bot muestra menú principal
3. Comandos disponibles:
   - `/track` o `/status` - Ver estado actual
   - `/settings` - Configuraciones

## 📊 **QUÉ HACE EL BOT**

### ✅ **Scraping Automático:**
- Extrae datos de `https://m.cubamax.com/trk/CODIGO`
- Obtiene todos los eventos del envío
- Identifica estados completados vs pendientes

### ✅ **Almacenamiento:**
- Guarda usuarios en hoja USERS
- Crea hoja individual por usuario en spreadsheet secundario
- Registra fecha/hora de entrada en hoja TIME

### ✅ **Notificaciones:**
- Función `sendDailyNotifications()` para notificaciones automáticas
- Configura trigger diario con `setupTriggers()`

## 🔧 **FUNCIONES PRINCIPALES**

```javascript
// Probar el bot
testBot();

// Configurar notificaciones diarias
setupTriggers();

// Configurar webhook
setWebhook();
```

## 📱 **COMANDOS DEL BOT**

- `/start` - Iniciar/registrarse
- `/track` - Ver estado del envío
- `/status` - Ver estado del envío  
- `/settings` - Configuraciones (próximamente)

## 🎯 **ESTRUCTURA DE DATOS**

### **Hoja USERS:**
| USER NAME | USER ID | USER NUMBER | USER MAIL | TRACKING NUMBER | TIME |
|-----------|---------|-------------|-----------|-----------------|------|
| juan123   | 123456  | +1234567890 | <EMAIL> | EDF6V-BPF | 2025-07-13... |

### **Hoja Individual del Usuario:**
| USER ID | TRACKING DATA | TIMESTAMP | STATUS |
|---------|---------------|-----------|--------|
| 123456  | {"trackingCode":"EDF6V-BPF"...} | 2025-07-13... | Shipped |

## 🚨 **SOLUCIÓN DE PROBLEMAS**

### **Bot no responde:**
1. Verifica token en BOT-TOKEN!A2
2. Verifica webhook configurado
3. Ejecuta `testBot()` para ver errores

### **No guarda datos:**
1. Verifica permisos de Google Sheets
2. Verifica que las hojas existen con nombres exactos
3. Revisa logs en Apps Script → Ejecuciones

### **Error de scraping:**
1. Verifica que Cheerio está habilitado
2. Prueba con código válido (ej: EDF6V-BPF)
3. Verifica acceso a internet desde Apps Script

## 📈 **PRÓXIMAS MEJORAS**

- Configuración de frecuencia de notificaciones
- Múltiples códigos de tracking por usuario
- Notificaciones por email
- Estadísticas de envíos

## 🎉 **¡LISTO!**

El bot está configurado para:
- ✅ Registrar usuarios automáticamente
- ✅ Hacer scraping de CubaMax
- ✅ Guardar datos en Google Sheets
- ✅ Mostrar estado de envíos
- ✅ Notificaciones diarias (con trigger)

**¡Tu bot de CubaMax está listo para usar!** 🚀
