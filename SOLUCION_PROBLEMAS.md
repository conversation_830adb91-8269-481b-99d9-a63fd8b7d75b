# 🔧 Solución de Problemas - CubaMax Tracker Bot

## 🚨 Problemas Identificados y Solucionados

### ❌ **Problema 1: Bot enviando mensaje de bienvenida en bucle**
**Causa:** Estados temporales se perdían entre ejecuciones
**Solución:** Cambiado a usar `PropertiesService` para persistir estados

### ❌ **Problema 2: No guarda datos en Google Sheets**
**Causa:** SPREADSHEET_ID no configurado correctamente
**Solución:** Sistema mejorado de configuración con validación

## 🛠️ Pasos para Solucionar

### 1. **Configurar SPREADSHEET_ID**
```javascript
// En Google Apps Script, ejecuta esta función UNA VEZ:
setSpreadsheetId("TU_ID_DE_SPREADSHEET_REAL");
```

**Para obtener el ID del Spreadsheet:**
1. Abre tu Google Sheet
2. Copia la URL: `https://docs.google.com/spreadsheets/d/1ABC...XYZ/edit`
3. El ID es la parte entre `/d/` y `/edit`: `1ABC...XYZ`

### 2. **Verificar Configuración**
```javascript
// Ejecuta esta función para diagnosticar problemas:
diagnosticarBot();
```

### 3. **Limpiar Estados Temporales**
```javascript
// Si el bot está en bucle, ejecuta:
limpiarDatosTemporales();
```

### 4. **Probar el Bot**
```javascript
// Ejecuta una prueba básica:
probarBot();
```

## 📋 Lista de Verificación

### ✅ **Configuración de Google Sheets**

**Hoja: BOT-TOKEN**
- [ ] A1: "BOT TOKEN"
- [ ] A2: Token real del bot (ej: `123456789:ABC...`)

**Hoja: USERS**
- [ ] A1: "USER NAME"
- [ ] B1: "USER ID"
- [ ] C1: "USER NUMBER"
- [ ] D1: "USER MAIL"
- [ ] E1: "TRACKING NUMBER"
- [ ] F1: "TIME"
- [ ] G1: "NOTIFICATION FREQUENCY"
- [ ] H1: "LAST NOTIFICATION"

**Hoja: USER DB**
- [ ] A1: "USER SHEET URL"
- [ ] A2: URL completa de otro Google Sheet

**Hoja: TIME**
- [ ] A1: "USER ID"
- [ ] B1: "ENTRY TIME"

### ✅ **Configuración de Google Apps Script**

- [ ] Código copiado completamente
- [ ] SPREADSHEET_ID configurado con `setSpreadsheetId()`
- [ ] Biblioteca Cheerio habilitada
- [ ] Webhook configurado
- [ ] Triggers configurados

## 🔍 Comandos de Diagnóstico

### **Verificar Configuración Completa**
```javascript
function verificarTodo() {
  console.log("=== VERIFICACIÓN COMPLETA ===");
  
  // 1. Verificar SPREADSHEET_ID
  const id = getSpreadsheetId();
  console.log("SPREADSHEET_ID:", id);
  
  // 2. Ejecutar diagnóstico
  diagnosticarBot();
  
  // 3. Verificar token
  try {
    const token = getBotToken();
    console.log("Token length:", token ? token.length : 0);
  } catch (e) {
    console.error("Error obteniendo token:", e.message);
  }
  
  // 4. Verificar estados temporales
  const props = PropertiesService.getScriptProperties().getProperties();
  const tempKeys = Object.keys(props).filter(k => 
    k.startsWith('state_') || k.startsWith('temp_') || k.startsWith('username_')
  );
  console.log("Estados temporales activos:", tempKeys.length);
}
```

### **Limpiar Todo y Reiniciar**
```javascript
function reiniciarBot() {
  console.log("=== REINICIANDO BOT ===");
  
  // Limpiar datos temporales
  limpiarDatosTemporales();
  
  // Verificar configuración
  diagnosticarBot();
  
  console.log("Bot reiniciado. Prueba enviando /start");
}
```

## 🐛 Errores Comunes

### **Error: "SPREADSHEET_ID no configurado"**
```javascript
// Solución:
setSpreadsheetId("1ABC...XYZ"); // Tu ID real
```

### **Error: "Hoja USERS no encontrada"**
- Verifica que la hoja se llame exactamente "USERS"
- Verifica permisos de acceso al spreadsheet

### **Error: "Token del bot NO configurado"**
- Ve a la hoja BOT-TOKEN
- En celda A2, pega tu token del bot
- Formato: `123456789:ABC-DEF1234ghIkl-zyx57W2v1u123ew11`

### **Error: "URL de USER DB NO configurada"**
- Crea otro Google Sheet para datos de usuarios
- Copia su URL completa
- Pégala en USER DB!A2

## 📱 Probar el Bot

### **Prueba Manual**
1. Ejecuta `reiniciarBot()` en Apps Script
2. Ve a Telegram
3. Envía `/start` a tu bot
4. Debería pedir tu teléfono (no bucle infinito)

### **Verificar Logs**
1. En Google Apps Script → Ejecuciones
2. Revisa los logs de cada ejecución
3. Busca errores en rojo

## 🔄 Flujo Correcto del Bot

1. **Usuario envía /start**
   - Bot verifica si usuario existe en USERS
   - Si NO existe: pide teléfono
   - Si existe: muestra menú principal

2. **Registro de nuevo usuario**
   - Pide teléfono → email → tracking
   - Guarda en hoja USERS
   - Crea hoja individual
   - Hace scraping inicial

3. **Usuario existente**
   - Limpia estados temporales
   - Muestra comandos disponibles

## 🆘 Si Nada Funciona

### **Opción 1: Recrear desde cero**
1. Crea nuevo proyecto en Apps Script
2. Copia el código actualizado
3. Configura todo de nuevo

### **Opción 2: Verificar permisos**
1. Apps Script → Permisos
2. Autorizar acceso a Google Sheets
3. Autorizar acceso a internet (UrlFetchApp)

### **Opción 3: Revisar webhook**
```javascript
// Reconfigurar webhook
setWebhook();
```

## 📞 Contacto de Soporte

Si sigues teniendo problemas:
1. Ejecuta `verificarTodo()` y copia los logs
2. Ejecuta `diagnosticarBot()` y copia los resultados
3. Incluye capturas de pantalla de las hojas de Google Sheets

## 🎯 Resultado Esperado

Después de seguir estos pasos:
- ✅ Bot responde a `/start` sin bucles
- ✅ Datos se guardan en Google Sheets
- ✅ Scraping funciona correctamente
- ✅ Notificaciones automáticas activas
