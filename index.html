<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Generador de URL Webhook Telegram</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f4f7fc;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 50px auto;
            background-color: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            font-size: 24px;
            color: #333;
        }
        .input-group {
            margin-bottom: 15px;
        }
        label {
            font-size: 14px;
            color: #555;
            display: block;
            margin-bottom: 8px;
        }
        input[type="text"], input[type="url"] {
            width: 100%;
            padding: 10px;
            font-size: 16px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
            margin-bottom: 10px;
        }
        button {
            width: 100%;
            padding: 10px;
            background-color: #3b82f6;
            border: none;
            color: white;
            font-size: 16px;
            cursor: pointer;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #2563eb;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            background-color: #e9f7ff;
            border-radius: 4px;
            word-wrap: break-word;
            font-size: 14px;
            color: #333;
        }
        .link-button {
            margin-top: 10px;
            width: 100%;
            padding: 10px;
            background-color: #10b981;
            border: none;
            color: white;
            font-size: 16px;
            cursor: pointer;
            border-radius: 4px;
            transition: background-color 0.3s;
            display: none; /* Inicialmente oculto */
        }
        .link-button:hover {
            background-color: #059669;
        }
    </style>
</head>
<body>

    <div class="container">
        <h1>Generador de URL Webhook para Telegram</h1>
        
        <div class="input-group">
            <label for="botToken">Token del Bot de Telegram:</label>
            <input type="text" id="botToken" placeholder="Introduce el token de tu bot">
        </div>
        
        <div class="input-group">
            <label for="scriptUrl">URL del Apps Script:</label>
            <input type="url" id="scriptUrl" placeholder="Introduce la URL de tu Apps Script">
        </div>

        <button onclick="generateWebhookUrl()">Generar URL Webhook</button>
        
        <div class="result" id="result">
            <p><strong>URL Generada:</strong></p>
            <p id="webhookUrl">Aquí aparecerá la URL generada...</p>
            <a id="openLinkButton" href="#" target="_blank" class="link-button">Abrir en una nueva pestaña</a>
        </div>
    </div>

    <script>
        function generateWebhookUrl() {
            const botToken = document.getElementById('botToken').value;
            const scriptUrl = document.getElementById('scriptUrl').value;

            if (botToken === '' || scriptUrl === '') {
                alert('Por favor, completa ambos campos.');
                return;
            }

            const webhookUrl = `https://api.telegram.org/bot${botToken}/setWebhook?url=${scriptUrl}/exec&allowed_updates=["callback_query","message"]&drop_pending_updates=True`;
            
            // Mostrar la URL generada
            document.getElementById('webhookUrl').textContent = webhookUrl;

            // Hacer visible el botón para abrir en una nueva pestaña
            const openLinkButton = document.getElementById('openLinkButton');
            openLinkButton.style.display = 'inline-block';
            openLinkButton.href = webhookUrl; // Asignar la URL generada al atributo href
        }
    </script>

</body>
</html>
